<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift template management permissions
requireAnyPermission($mysqli, ['shifts.view', 'shifts.create', 'shifts.edit']);

$msg = "";
$error = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create')) {
        $error = "You don't have permission to create shift templates.";
      } else {
        $name = trim($_POST['name'] ?? '');
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $desc = trim($_POST['description'] ?? '');

        // Attendance policy fields
        $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 1.00);
        $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0.50);
        $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
        $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0.50);
        $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 2.00);
        $overtime_tracking = isset($_POST['overtime_tracking_enabled']) ? 1 : 0;
        $weekend_policy_enabled = isset($_POST['weekend_policy_enabled']) ? 1 : 0;
        $holiday_policy_enabled = isset($_POST['holiday_policy_enabled']) ? 1 : 0;

        if ($name && $start_time && $end_time) {
          $stmt = $mysqli->prepare("
            INSERT INTO shift_templates (
              name, start_time, end_time, description,
              early_checkin_allowed_hours, late_checkin_grace_hours, late_checkin_policy,
              early_checkout_penalty_hours, late_checkout_overtime_hours, overtime_tracking_enabled,
              weekend_policy_enabled, holiday_policy_enabled
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ");
          $stmt->bind_param('ssssddsddiiii',
            $name, $start_time, $end_time, $desc,
            $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
            $early_checkout_penalty, $late_checkout_overtime, $overtime_tracking,
            $weekend_policy_enabled, $holiday_policy_enabled
          );
          if ($stmt->execute()) {
            $msg = "Shift template \"$name\" created successfully with attendance policies.";
          } else {
            $error = "Failed to create shift template. It may already exist.";
          }
          $stmt->close();
        } else {
          $error = "Please fill in all required fields.";
        }
      }
    } elseif ($action === 'edit_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit')) {
        $error = "You don't have permission to edit shift templates.";
      } else {
        $template_id = intval($_POST['template_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $desc = trim($_POST['description'] ?? '');

        // Attendance policy fields
        $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 1.00);
        $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0.50);
        $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
        $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0.50);
        $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 2.00);
        $overtime_tracking = isset($_POST['overtime_tracking_enabled']) ? 1 : 0;
        $weekend_policy_enabled = isset($_POST['weekend_policy_enabled']) ? 1 : 0;
        $holiday_policy_enabled = isset($_POST['holiday_policy_enabled']) ? 1 : 0;

        if ($template_id && $name && $start_time && $end_time) {
          $stmt = $mysqli->prepare("
            UPDATE shift_templates SET
              name = ?, start_time = ?, end_time = ?, description = ?,
              early_checkin_allowed_hours = ?, late_checkin_grace_hours = ?, late_checkin_policy = ?,
              early_checkout_penalty_hours = ?, late_checkout_overtime_hours = ?, overtime_tracking_enabled = ?,
              weekend_policy_enabled = ?, holiday_policy_enabled = ?
            WHERE id = ?
          ");
          $stmt->bind_param('ssssddsddiiii',
            $name, $start_time, $end_time, $desc,
            $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
            $early_checkout_penalty, $late_checkout_overtime, $overtime_tracking,
            $weekend_policy_enabled, $holiday_policy_enabled, $template_id
          );
          if ($stmt->execute()) {
            $msg = "Shift template updated successfully with attendance policies.";
          } else {
            $error = "Failed to update shift template.";
          }
          $stmt->close();
        } else {
          $error = "Please fill in all required fields.";
        }
      }
    } elseif ($action === 'delete_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.delete')) {
        $error = "You don't have permission to delete shift templates.";
      } else {
        $template_id = intval($_POST['template_id'] ?? 0);
        if ($template_id) {
          // Check if template is being used
          $check_stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM employee_shifts WHERE shift_template_id = ?");
          $check_stmt->bind_param('i', $template_id);
          $check_stmt->execute();
          $check_stmt->bind_result($usage_count);
          $check_stmt->fetch();
          $check_stmt->close();

          if ($usage_count > 0) {
            $error = "Cannot delete shift template. It is currently assigned to $usage_count employee(s).";
          } else {
            $stmt = $mysqli->prepare("DELETE FROM shift_templates WHERE id = ?");
            $stmt->bind_param('i', $template_id);
            if ($stmt->execute()) {
              $msg = "Shift template deleted successfully.";
            } else {
              $error = "Failed to delete shift template.";
            }
            $stmt->close();
          }
        }
      }
    }
  }
}

// Fetch shift templates with attendance policies
$templates_result = $mysqli->query("
  SELECT *,
    CASE
      WHEN weekend_policy_enabled = 1 THEN 'Weekend policies enabled'
      ELSE ''
    END as weekend_status,
    CASE
      WHEN holiday_policy_enabled = 1 THEN 'Holiday policies enabled'
      ELSE ''
    END as holiday_status
  FROM shift_templates
  ORDER BY name
");
$shift_templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Check user permissions for UI
$can_create = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create');
$can_edit = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit');
$can_delete = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.delete');
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shift Templates - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">


    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Available Templates -->
    <div class="content-card">
      <?php if (empty($shift_templates)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Shift Templates Found</h4>
          <p class="text-muted">
            <?php if ($can_create): ?>
              <a href="manage_templates.php" class="btn btn-primary">Create your first shift template</a>
            <?php else: ?>
              Contact an administrator to create shift templates.
            <?php endif; ?>
          </p>
        </div>
      <?php else: ?>
        <div class="row g-3">
          <?php foreach ($shift_templates as $template): ?>
            <div class="col-md-6 col-lg-4">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title"><?= htmlspecialchars($template['name']) ?></h5>
                  <p class="card-text">
                    <strong>Time:</strong> <?= date('g:i A', strtotime($template['start_time'])) ?> - <?= date('g:i A', strtotime($template['end_time'])) ?><br>
                    <strong>Duration:</strong>
                    <?php
                    $start = new DateTime($template['start_time']);
                    $end = new DateTime($template['end_time']);
                    if ($end < $start) $end->add(new DateInterval('P1D')); // Next day
                    $duration = $start->diff($end);
                    echo $duration->format('%h hours %i minutes');
                    ?>
                  </p>
                  <?php if ($template['description']): ?>
                    <p class="card-text text-muted"><?= htmlspecialchars($template['description']) ?></p>
                  <?php endif; ?>

                  <div class="mt-3">
                    <h6 class="text-muted">Attendance Policies:</h6>
                    <small class="text-muted">
                      <div><strong>Early Check-in:</strong> <?= $template['early_checkin_allowed_hours'] ?>h</div>
                      <div><strong>Grace Period:</strong> <?= $template['late_checkin_grace_hours'] ?>h</div>
                      <div><strong>Overtime Tracking:</strong> <?= $template['overtime_tracking_enabled'] ? 'Enabled' : 'Disabled' ?></div>
                      <?php if ($template['weekend_policy_enabled']): ?>
                        <span class="badge bg-info me-1">Weekend Policies</span>
                      <?php endif; ?>
                      <?php if ($template['holiday_policy_enabled']): ?>
                        <span class="badge bg-warning">Holiday Policies</span>
                      <?php endif; ?>
                    </small>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="d-flex gap-2">
                    <a href="assign.php?template_id=<?= $template['id'] ?>" class="btn btn-primary btn-sm">
                      <i class="bi bi-calendar-plus me-1"></i>
                      Use Template
                    </a>
                    <?php if ($can_edit): ?>
                    <button type="button" class="btn btn-outline-secondary btn-sm"
                            onclick="editTemplate(<?= $template['id'] ?>,
                              '<?= htmlspecialchars($template['name']) ?>',
                              '<?= $template['start_time'] ?>',
                              '<?= $template['end_time'] ?>',
                              '<?= htmlspecialchars($template['description']) ?>',
                              <?= $template['early_checkin_allowed_hours'] ?>,
                              <?= $template['late_checkin_grace_hours'] ?>,
                              '<?= $template['late_checkin_policy'] ?>',
                              <?= $template['early_checkout_penalty_hours'] ?>,
                              <?= $template['late_checkout_overtime_hours'] ?>,
                              <?= $template['overtime_tracking_enabled'] ? 'true' : 'false' ?>,
                              <?= $template['weekend_policy_enabled'] ? 'true' : 'false' ?>,
                              <?= $template['holiday_policy_enabled'] ? 'true' : 'false' ?>)">
                      <i class="bi bi-pencil me-1"></i>
                      Edit
                    </button>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
  </div>

  <!-- Edit Template Modal -->
  <div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Edit Shift Template</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form method="POST" id="editTemplateForm">
          <div class="modal-body">
            <input type="hidden" name="action" value="edit_template" />
            <input type="hidden" name="template_id" id="edit_template_id" />

            <div class="row g-3">
              <div class="col-12">
                <label for="edit_name" class="form-label">Template Name</label>
                <input type="text" class="form-control" id="edit_name" name="name" required />
              </div>

              <div class="col-6">
                <label for="edit_start_time" class="form-label">Start Time</label>
                <input type="time" class="form-control" id="edit_start_time" name="start_time" required />
              </div>
              <div class="col-6">
                <label for="edit_end_time" class="form-label">End Time</label>
                <input type="time" class="form-control" id="edit_end_time" name="end_time" required />
              </div>

              <div class="col-12">
                <label for="edit_description" class="form-label">Description</label>
                <textarea class="form-control" id="edit_description" name="description" rows="2"></textarea>
              </div>

              <!-- Attendance Policies -->
              <div class="col-12">
                <hr class="my-3">
                <h6><i class="bi bi-clock-history me-2"></i>Attendance Policies</h6>
              </div>

              <div class="col-6">
                <label for="edit_early_checkin_allowed_hours" class="form-label">Early Check-in Allowed (hours)</label>
                <input type="number" step="0.25" min="0" max="24" class="form-control"
                       id="edit_early_checkin_allowed_hours" name="early_checkin_allowed_hours" />
              </div>

              <div class="col-6">
                <label for="edit_late_checkin_grace_hours" class="form-label">Late Check-in Grace Period (hours)</label>
                <input type="number" step="0.25" min="0" max="24" class="form-control"
                       id="edit_late_checkin_grace_hours" name="late_checkin_grace_hours" />
              </div>

              <div class="col-6">
                <label for="edit_late_checkin_policy" class="form-label">Late Check-in Policy</label>
                <select class="form-select" id="edit_late_checkin_policy" name="late_checkin_policy">
                  <option value="mark_late">Mark as Late</option>
                  <option value="mark_absent">Mark as Absent</option>
                </select>
              </div>

              <div class="col-6">
                <label for="edit_early_checkout_penalty_hours" class="form-label">Early Check-out Penalty (hours)</label>
                <input type="number" step="0.25" min="0" max="24" class="form-control"
                       id="edit_early_checkout_penalty_hours" name="early_checkout_penalty_hours" />
              </div>

              <div class="col-6">
                <label for="edit_late_checkout_overtime_hours" class="form-label">Late Check-out Overtime Limit (hours)</label>
                <input type="number" step="0.25" min="0" max="24" class="form-control"
                       id="edit_late_checkout_overtime_hours" name="late_checkout_overtime_hours" />
              </div>

              <div class="col-12">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="edit_overtime_tracking_enabled" name="overtime_tracking_enabled">
                  <label class="form-check-label" for="edit_overtime_tracking_enabled">
                    Track Overtime Hours
                  </label>
                </div>
              </div>

              <div class="col-6">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="edit_weekend_policy_enabled" name="weekend_policy_enabled">
                  <label class="form-check-label" for="edit_weekend_policy_enabled">
                    Enable Different Weekend Policies
                  </label>
                </div>
              </div>

              <div class="col-6">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="edit_holiday_policy_enabled" name="holiday_policy_enabled">
                  <label class="form-check-label" for="edit_holiday_policy_enabled">
                    Enable Different Holiday Policies
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary">Update Template</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function editTemplate(id, name, startTime, endTime, description,
                         earlyCheckinAllowed, lateCheckinGrace, lateCheckinPolicy,
                         earlyCheckoutPenalty, lateCheckoutOvertime, overtimeTrackingEnabled,
                         weekendPolicyEnabled, holidayPolicyEnabled) {
      // Basic fields
      document.getElementById('edit_template_id').value = id;
      document.getElementById('edit_name').value = name;
      document.getElementById('edit_start_time').value = startTime;
      document.getElementById('edit_end_time').value = endTime;
      document.getElementById('edit_description').value = description;

      // Attendance policy fields
      document.getElementById('edit_early_checkin_allowed_hours').value = earlyCheckinAllowed;
      document.getElementById('edit_late_checkin_grace_hours').value = lateCheckinGrace;
      document.getElementById('edit_late_checkin_policy').value = lateCheckinPolicy;
      document.getElementById('edit_early_checkout_penalty_hours').value = earlyCheckoutPenalty;
      document.getElementById('edit_late_checkout_overtime_hours').value = lateCheckoutOvertime;
      document.getElementById('edit_overtime_tracking_enabled').checked = overtimeTrackingEnabled === 'true';
      document.getElementById('edit_weekend_policy_enabled').checked = weekendPolicyEnabled === 'true';
      document.getElementById('edit_holiday_policy_enabled').checked = holidayPolicyEnabled === 'true';

      new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
    }
  </script>
</body>
</html>
