<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit;
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

// Check if user has permission to manage attendance policies
if (!hasPermission($mysqli, $_SESSION['user_id'], 'attendance.manage_policies')) {
    header("Location: ../dashboard.php?error=" . urlencode("You don't have permission to manage attendance policies."));
    exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

$msg = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'save_policy') {
            $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
            $policy_type = $_POST['policy_type'] ?? '';
            
            if ($shift_template_id && in_array($policy_type, ['weekend', 'holiday'])) {
                // Policy fields
                $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 0);
                $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0);
                $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
                $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0);
                $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 0);
                $overtime_tracking = isset($_POST['overtime_tracking_enabled']) ? 1 : 0;
                
                // Check if policy already exists
                $check_stmt = $mysqli->prepare("
                    SELECT id FROM attendance_policies 
                    WHERE shift_template_id = ? AND policy_type = ?
                ");
                $check_stmt->bind_param('is', $shift_template_id, $policy_type);
                $check_stmt->execute();
                $existing = $check_stmt->get_result()->fetch_assoc();
                $check_stmt->close();
                
                if ($existing) {
                    // Update existing policy
                    $stmt = $mysqli->prepare("
                        UPDATE attendance_policies SET
                            early_checkin_allowed_hours = ?, late_checkin_grace_hours = ?,
                            late_checkin_policy = ?, early_checkout_penalty_hours = ?,
                            late_checkout_overtime_hours = ?, overtime_tracking_enabled = ?
                        WHERE shift_template_id = ? AND policy_type = ?
                    ");
                    $stmt->bind_param('ddsddiis', 
                        $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
                        $early_checkout_penalty, $late_checkout_overtime, $overtime_tracking,
                        $shift_template_id, $policy_type
                    );
                } else {
                    // Create new policy
                    $stmt = $mysqli->prepare("
                        INSERT INTO attendance_policies (
                            shift_template_id, policy_type, early_checkin_allowed_hours,
                            late_checkin_grace_hours, late_checkin_policy, early_checkout_penalty_hours,
                            late_checkout_overtime_hours, overtime_tracking_enabled
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->bind_param('isddsddi', 
                        $shift_template_id, $policy_type, $early_checkin_allowed, $late_checkin_grace,
                        $late_checkin_policy, $early_checkout_penalty, $late_checkout_overtime, $overtime_tracking
                    );
                }
                
                if ($stmt->execute()) {
                    $msg = ucfirst($policy_type) . " policy saved successfully.";
                    
                    // Enable the policy in the shift template
                    $enable_field = $policy_type . '_policy_enabled';
                    $enable_stmt = $mysqli->prepare("UPDATE shift_templates SET $enable_field = 1 WHERE id = ?");
                    $enable_stmt->bind_param('i', $shift_template_id);
                    $enable_stmt->execute();
                    $enable_stmt->close();
                } else {
                    $error = "Failed to save policy.";
                }
                $stmt->close();
            } else {
                $error = "Invalid policy data.";
            }
        } elseif ($action === 'delete_policy') {
            $policy_id = intval($_POST['policy_id'] ?? 0);
            $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
            $policy_type = $_POST['policy_type'] ?? '';
            
            if ($policy_id) {
                $stmt = $mysqli->prepare("DELETE FROM attendance_policies WHERE id = ?");
                $stmt->bind_param('i', $policy_id);
                if ($stmt->execute()) {
                    $msg = ucfirst($policy_type) . " policy deleted successfully.";
                    
                    // Disable the policy in the shift template
                    $enable_field = $policy_type . '_policy_enabled';
                    $disable_stmt = $mysqli->prepare("UPDATE shift_templates SET $enable_field = 0 WHERE id = ?");
                    $disable_stmt->bind_param('i', $shift_template_id);
                    $disable_stmt->execute();
                    $disable_stmt->close();
                } else {
                    $error = "Failed to delete policy.";
                }
                $stmt->close();
            }
        }
    }
}

// Fetch shift templates
$templates_result = $mysqli->query("
    SELECT id, name, start_time, end_time, weekend_policy_enabled, holiday_policy_enabled
    FROM shift_templates 
    ORDER BY name
");
$shift_templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Fetch existing policies
$policies_result = $mysqli->query("
    SELECT ap.*, st.name as shift_name
    FROM attendance_policies ap
    JOIN shift_templates st ON ap.shift_template_id = st.id
    ORDER BY st.name, ap.policy_type
");
$existing_policies = $policies_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Weekend & Holiday Policies - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Weekend & Holiday Policies</h1>
          <p class="page-subtitle">Configure different attendance rules for weekends and holidays</p>
        </div>
        <div class="d-flex gap-3">
          <a href="../dashboard.php" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Policy Configuration -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-gear me-2"></i>
        Configure Policy
      </h2>
      <form method="POST" class="row g-3">
        <input type="hidden" name="action" value="save_policy" />

        <div class="col-md-6">
          <label for="shift_template_id" class="form-label">Shift Template</label>
          <select class="form-select" id="shift_template_id" name="shift_template_id" required>
            <option value="">Select Shift Template</option>
            <?php foreach ($shift_templates as $template): ?>
              <option value="<?= $template['id'] ?>">
                <?= htmlspecialchars($template['name']) ?> 
                (<?= date('g:i A', strtotime($template['start_time'])) ?> - <?= date('g:i A', strtotime($template['end_time'])) ?>)
              </option>
            <?php endforeach; ?>
          </select>
        </div>

        <div class="col-md-6">
          <label for="policy_type" class="form-label">Policy Type</label>
          <select class="form-select" id="policy_type" name="policy_type" required>
            <option value="">Select Policy Type</option>
            <option value="weekend">Weekend Policy</option>
            <option value="holiday">Holiday Policy</option>
          </select>
        </div>

        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            <strong>Policy Purpose:</strong> Weekend and holiday policies allow you to set different attendance rules 
            for non-regular work days. For example, you might want to disable grace periods on weekends or 
            have stricter overtime limits on holidays.
          </div>
        </div>

        <div class="col-md-6">
          <label for="early_checkin_allowed_hours" class="form-label">Early Check-in Allowed (hours)</label>
          <input type="number" class="form-control" id="early_checkin_allowed_hours" 
                 name="early_checkin_allowed_hours" step="0.25" min="0" max="8" value="0" />
          <small class="form-text text-muted">Set to 0 to disable early check-in on weekends/holidays</small>
        </div>

        <div class="col-md-6">
          <label for="late_checkin_grace_hours" class="form-label">Late Check-in Grace Period (hours)</label>
          <input type="number" class="form-control" id="late_checkin_grace_hours" 
                 name="late_checkin_grace_hours" step="0.25" min="0" max="4" value="0" />
          <small class="form-text text-muted">Set to 0 for no grace period on weekends/holidays</small>
        </div>

        <div class="col-md-6">
          <label for="late_checkin_policy" class="form-label">Late Check-in Policy</label>
          <select class="form-select" id="late_checkin_policy" name="late_checkin_policy">
            <option value="mark_late">Mark as Late</option>
            <option value="mark_absent">Mark as Absent</option>
          </select>
          <small class="form-text text-muted">Action for arrivals beyond grace period</small>
        </div>

        <div class="col-md-6">
          <label for="early_checkout_penalty_hours" class="form-label">Early Check-out Penalty (hours)</label>
          <input type="number" class="form-control" id="early_checkout_penalty_hours" 
                 name="early_checkout_penalty_hours" step="0.25" min="0" max="4" value="0" />
          <small class="form-text text-muted">Hours before shift end that trigger penalty</small>
        </div>

        <div class="col-md-6">
          <label for="late_checkout_overtime_hours" class="form-label">Late Check-out Overtime Limit (hours)</label>
          <input type="number" class="form-control" id="late_checkout_overtime_hours" 
                 name="late_checkout_overtime_hours" step="0.25" min="0" max="8" value="0" />
          <small class="form-text text-muted">Set to 0 to disable overtime on weekends/holidays</small>
        </div>

        <div class="col-md-6">
          <div class="form-check form-switch mt-4">
            <input class="form-check-input" type="checkbox" id="overtime_tracking_enabled" name="overtime_tracking_enabled">
            <label class="form-check-label" for="overtime_tracking_enabled">
              <strong>Track Overtime Hours</strong>
            </label>
            <small class="form-text text-muted d-block">Enable overtime hour tracking for this policy</small>
          </div>
        </div>

        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-save me-1"></i>
            Save Policy
          </button>
        </div>
      </form>
    </div>

    <!-- Existing Policies -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-list-check me-2"></i>
        Existing Policies
      </h2>

      <?php if (empty($existing_policies)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Policies Configured</h4>
          <p class="text-muted">Create your first weekend or holiday policy to get started.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Shift Template</th>
                <th>Policy Type</th>
                <th>Early Check-in</th>
                <th>Grace Period</th>
                <th>Late Policy</th>
                <th>Overtime Limit</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($existing_policies as $policy): ?>
              <tr>
                <td>
                  <strong><?= htmlspecialchars($policy['shift_name']) ?></strong>
                </td>
                <td>
                  <span class="badge bg-<?= $policy['policy_type'] === 'weekend' ? 'info' : 'warning' ?>">
                    <?= ucfirst($policy['policy_type']) ?>
                  </span>
                </td>
                <td><?= $policy['early_checkin_allowed_hours'] ?>h</td>
                <td><?= $policy['late_checkin_grace_hours'] ?>h</td>
                <td>
                  <span class="badge bg-<?= $policy['late_checkin_policy'] === 'mark_absent' ? 'danger' : 'warning' ?>">
                    <?= ucwords(str_replace('_', ' ', $policy['late_checkin_policy'])) ?>
                  </span>
                </td>
                <td><?= $policy['late_checkout_overtime_hours'] ?>h</td>
                <td>
                  <form method="POST" style="display: inline;"
                        onsubmit="return confirm('Are you sure you want to delete this policy?')">
                    <input type="hidden" name="action" value="delete_policy" />
                    <input type="hidden" name="policy_id" value="<?= $policy['id'] ?>" />
                    <input type="hidden" name="shift_template_id" value="<?= $policy['shift_template_id'] ?>" />
                    <input type="hidden" name="policy_type" value="<?= $policy['policy_type'] ?>" />
                    <button type="submit" class="btn btn-outline-danger btn-sm">
                      <i class="bi bi-trash"></i>
                    </button>
                  </form>
                </td>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>

    <!-- Policy Examples -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-lightbulb me-2"></i>
        Policy Examples
      </h2>
      <div class="row">
        <div class="col-md-6">
          <div class="card border-info">
            <div class="card-header bg-info text-white">
              <h6 class="mb-0">Strict Weekend Policy</h6>
            </div>
            <div class="card-body">
              <ul class="list-unstyled mb-0">
                <li><strong>Early Check-in:</strong> 0 hours (disabled)</li>
                <li><strong>Grace Period:</strong> 0 hours (no grace)</li>
                <li><strong>Late Policy:</strong> Mark as Absent</li>
                <li><strong>Overtime:</strong> 0 hours (disabled)</li>
              </ul>
              <small class="text-muted">Use for strict weekend attendance with no flexibility</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
              <h6 class="mb-0">Flexible Holiday Policy</h6>
            </div>
            <div class="card-body">
              <ul class="list-unstyled mb-0">
                <li><strong>Early Check-in:</strong> 2 hours</li>
                <li><strong>Grace Period:</strong> 1 hour</li>
                <li><strong>Late Policy:</strong> Mark as Late</li>
                <li><strong>Overtime:</strong> 4 hours</li>
              </ul>
              <small class="text-muted">Use for holidays with extended flexibility</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
