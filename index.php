<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/includes/config.php';
require_once __DIR__ . '/includes/permissions.php';

if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("DB connection failed.");
}

// Check if user is super admin - redirect them to dashboard
$user_roles = $_SESSION['roles'] ?? [];
$is_super_admin = in_array('super_admin', $user_roles);

if ($is_super_admin) {
  header("Location: " . appUrl('dashboard.php') . "?msg=" . urlencode("Super admins don't need to clock in/out. Use this dashboard to manage the system."));
  exit;
}

// Check permissions
requireAnyPermission($mysqli, ['attendance.clock_in', 'attendance.clock_out']);

$user_permissions = $_SESSION['permissions'] ?? [];
$user_roles = $_SESSION['roles'] ?? [];
$employee_id = $_SESSION['employee_id'] ?? 0;

// Determine if user can manage others' attendance
$can_manage_others = hasAnyPermission($mysqli, $_SESSION['user_id'], ['attendance.view_all', 'employees.view']);

// Fetch employees for admin/managers, or single employee record for normal employee
if ($can_manage_others) {
  $result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
  $employees = [];
  while ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
} else {
  $result = $mysqli->query("SELECT id, name FROM employees WHERE id = " . intval($employee_id));
  $employees = [];
  if ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Clock In/Out - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <?php include 'includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Clock In/Out</h1>
          <p class="page-subtitle">Record your attendance with location and photo verification</p>
        </div>
        <div>
          <a href="dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if (isset($_GET['msg'])): ?>
      <div class="alert alert-info alert-dismissible fade show">
        <i class="bi bi-info-circle me-2"></i>
        <?= htmlspecialchars($_GET['msg']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <!-- Clock Form -->
    <div class="content-card">
      <form id="clockForm" method="POST" action="submit_attendance.php" enctype="multipart/form-data" novalidate>

        <!-- Employee Selection -->
        <div class="form-group">
          <label for="employee" class="form-label">
            <i class="bi bi-person me-2"></i>
            Select Employee
          </label>
          <select name="employee_id" id="employee" class="form-select" required <?=(!$can_manage_others) ? 'disabled' : ''?>>
            <option value="">Choose an employee</option>
            <?php foreach($employees as $emp): ?>
              <option value="<?= $emp['id'] ?>" <?= ($emp['id'] == $employee_id) ? 'selected' : '' ?>>
                <?= htmlspecialchars($emp['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
          <?php if (!$can_manage_others): ?>
            <input type="hidden" name="employee_id" value="<?= intval($employee_id) ?>" />
          <?php endif; ?>
        </div>

        <!-- Action Buttons -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-clock me-2"></i>
            Choose Action
          </label>
          <div class="d-grid gap-2 d-md-flex">
            <button type="button" id="clockInBtn" class="btn btn-success flex-fill">
              <i class="bi bi-play-circle me-2"></i>
              Clock In
            </button>
            <button type="button" id="clockOutBtn" class="btn btn-danger flex-fill">
              <i class="bi bi-stop-circle me-2"></i>
              Clock Out
            </button>
          </div>
          <input type="hidden" name="type" id="type" />
        </div>

        <!-- Location -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-geo-alt me-2"></i>
            Your Location
          </label>
          <input type="text" id="locationDisplay" disabled class="form-control" placeholder="Detecting location..." />
          <input type="hidden" name="lat" id="lat" />
          <input type="hidden" name="lng" id="lng" />
        </div>

        <!-- Camera Section -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-camera me-2"></i>
            Take a Selfie
          </label>
          <div class="camera-container">
            <video id="video" autoplay playsinline class="camera-video"></video>
            <canvas id="canvas" style="display:none;"></canvas>
            <div class="camera-overlay">
              <button type="button" class="btn btn-light btn-sm" onclick="capturePhoto()">
                <i class="bi bi-camera"></i>
                Capture
              </button>
            </div>
          </div>
          <input type="hidden" name="selfie" id="selfie" />
        </div>

        <!-- Map -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-map me-2"></i>
            Location Map
          </label>
          <div id="map" class="map-container"></div>
        </div>

        <!-- Submit Button -->
        <button type="submit" id="submitBtn" class="btn btn-primary w-100" disabled>
          <i class="bi bi-check-circle me-2"></i>
          Submit Attendance
        </button>
      </form>
    </div>
  </div>

  <style>
    .camera-container {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      background: #f8f9fa;
      border: 2px solid var(--border-color);
    }

    .camera-video {
      width: 100%;
      height: 300px;
      object-fit: cover;
      border-radius: 10px;
    }

    .camera-overlay {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .map-container {
      height: 250px;
      border-radius: 12px;
      overflow: hidden;
      border: 2px solid var(--border-color);
    }

    #submitBtn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .form-group {
      margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
      .camera-video {
        height: 250px;
      }

      .map-container {
        height: 200px;
      }
    }
  </style>

<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
let map;
let marker;
let video;
let canvas;
let stream;
let currentLat = null;
let currentLng = null;
let photoTaken = false;

document.addEventListener('DOMContentLoaded', function() {
  initializeLocation();
  initializeCamera();
  setupEventListeners();
});

function setupEventListeners() {
  document.getElementById('clockInBtn').addEventListener('click', function() {
    document.getElementById('type').value = 'in';
    this.classList.add('active');
    document.getElementById('clockOutBtn').classList.remove('active');
    checkFormCompletion();
  });

  document.getElementById('clockOutBtn').addEventListener('click', function() {
    document.getElementById('type').value = 'out';
    this.classList.add('active');
    document.getElementById('clockInBtn').classList.remove('active');
    checkFormCompletion();
  });

  document.getElementById('clockForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';
    submitBtn.disabled = true;

    // Submit the form
    this.submit();
  });
}

function initializeLocation() {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      function(position) {
        currentLat = position.coords.latitude;
        currentLng = position.coords.longitude;

        document.getElementById('lat').value = currentLat;
        document.getElementById('lng').value = currentLng;
        document.getElementById('locationDisplay').value = `${currentLat.toFixed(6)}, ${currentLng.toFixed(6)}`;

        initializeMap();
        checkFormCompletion();
      },
      function(error) {
        console.error('Geolocation error:', error);
        document.getElementById('locationDisplay').value = 'Location access denied or unavailable';
        document.getElementById('locationDisplay').classList.add('is-invalid');

        // Initialize map with default location
        initializeMap(0, 0);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  } else {
    document.getElementById('locationDisplay').value = 'Geolocation not supported';
    document.getElementById('locationDisplay').classList.add('is-invalid');
    initializeMap(0, 0);
  }
}

function initializeMap(lat = currentLat, lng = currentLng) {
  if (map) {
    map.remove();
  }

  map = L.map('map').setView([lat || 0, lng || 0], lat && lng ? 15 : 2);

  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
  }).addTo(map);

  if (lat && lng) {
    marker = L.marker([lat, lng]).addTo(map)
      .bindPopup('Your current location')
      .openPopup();
  }
}

function initializeCamera() {
  video = document.getElementById('video');
  canvas = document.getElementById('canvas');

  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
    navigator.mediaDevices.getUserMedia({
      video: {
        facingMode: 'user',
        width: { ideal: 640 },
        height: { ideal: 480 }
      }
    })
    .then(function(mediaStream) {
      stream = mediaStream;
      video.srcObject = stream;
      video.play();
    })
    .catch(function(error) {
      console.error('Camera error:', error);
      video.style.display = 'none';
      const cameraContainer = document.querySelector('.camera-container');
      cameraContainer.innerHTML = `
        <div class="alert alert-warning">
          <i class="bi bi-camera-video-off me-2"></i>
          Camera access denied or unavailable. Please enable camera access and refresh the page.
        </div>
      `;
    });
  } else {
    video.style.display = 'none';
    const cameraContainer = document.querySelector('.camera-container');
    cameraContainer.innerHTML = `
      <div class="alert alert-danger">
        <i class="bi bi-camera-video-off me-2"></i>
        Camera not supported on this device.
      </div>
    `;
  }
}

function capturePhoto() {
  if (!video || !canvas) return;

  const context = canvas.getContext('2d');
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;

  context.drawImage(video, 0, 0, canvas.width, canvas.height);

  const dataURL = canvas.toDataURL('image/jpeg', 0.8);
  document.getElementById('selfie').value = dataURL;

  // Show preview
  video.style.display = 'none';
  canvas.style.display = 'block';

  // Update capture button
  const overlay = document.querySelector('.camera-overlay');
  overlay.innerHTML = `
    <button type="button" class="btn btn-success btn-sm me-2" disabled>
      <i class="bi bi-check-circle"></i>
      Photo Captured
    </button>
    <button type="button" class="btn btn-outline-light btn-sm" onclick="retakePhoto()">
      <i class="bi bi-arrow-clockwise"></i>
      Retake
    </button>
  `;

  photoTaken = true;
  checkFormCompletion();
}

function retakePhoto() {
  video.style.display = 'block';
  canvas.style.display = 'none';
  document.getElementById('selfie').value = '';

  const overlay = document.querySelector('.camera-overlay');
  overlay.innerHTML = `
    <button type="button" class="btn btn-light btn-sm" onclick="capturePhoto()">
      <i class="bi bi-camera"></i>
      Capture
    </button>
  `;

  photoTaken = false;
  checkFormCompletion();
}

function validateForm() {
  const employeeId = document.getElementById('employee').value;
  const type = document.getElementById('type').value;
  const lat = document.getElementById('lat').value;
  const lng = document.getElementById('lng').value;
  const selfie = document.getElementById('selfie').value;

  if (!employeeId) {
    alert('Please select an employee.');
    return false;
  }

  if (!type) {
    alert('Please select Clock In or Clock Out.');
    return false;
  }

  if (!lat || !lng) {
    alert('Location is required. Please enable location access.');
    return false;
  }

  if (!selfie) {
    alert('Please take a selfie photo.');
    return false;
  }

  return true;
}

function checkFormCompletion() {
  const employeeId = document.getElementById('employee').value;
  const type = document.getElementById('type').value;
  const hasLocation = currentLat && currentLng;
  const submitBtn = document.getElementById('submitBtn');

  if (employeeId && type && hasLocation && photoTaken) {
    submitBtn.disabled = false;
    submitBtn.classList.remove('btn-secondary');
    submitBtn.classList.add('btn-primary');
  } else {
    submitBtn.disabled = true;
    submitBtn.classList.remove('btn-primary');
    submitBtn.classList.add('btn-secondary');
  }
}

// Real-time validation and warnings
let currentShiftData = null;
let validationInterval = null;

// Fetch current shift data for validation
async function fetchShiftData() {
  try {
    const response = await fetch('api/get_shift_info.php');
    const data = await response.json();
    currentShiftData = data;
    updateValidationDisplay();
  } catch (error) {
    console.error('Error fetching shift data:', error);
  }
}

function updateValidationDisplay() {
  if (!currentShiftData || !currentShiftData.shift) {
    showValidationMessage('No shift assigned for today', 'warning');
    return;
  }

  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();
  const shift = currentShiftData.shift;

  // Parse shift times
  const shiftStart = parseTime(shift.start_time);
  const shiftEnd = parseTime(shift.end_time);

  // Calculate policy windows
  const earlyCheckinStart = shiftStart - (shift.early_checkin_allowed_hours * 60);
  const lateCheckinLimit = shiftStart + (shift.late_checkin_grace_hours * 60);

  // Determine current status and show appropriate message
  if (currentTime < earlyCheckinStart) {
    const minutesUntilEarly = earlyCheckinStart - currentTime;
    showValidationMessage(`Too early to check in. You can check in ${formatMinutes(minutesUntilEarly)} from now for overtime.`, 'info');
  } else if (currentTime < shiftStart) {
    const overtimeMinutes = shiftStart - currentTime;
    showValidationMessage(`✅ You can check in now for ${formatMinutes(overtimeMinutes)} of overtime!`, 'success');
  } else if (currentTime <= lateCheckinLimit) {
    if (currentTime === shiftStart) {
      showValidationMessage('✅ Perfect timing! Check in now for on-time attendance.', 'success');
    } else {
      const lateMinutes = currentTime - shiftStart;
      showValidationMessage(`⚠️ You are ${formatMinutes(lateMinutes)} late, but within grace period.`, 'warning');
    }
  } else {
    const lateMinutes = currentTime - shiftStart;
    const policy = shift.late_checkin_policy === 'mark_absent' ? 'marked as absent' : 'marked as very late';
    showValidationMessage(`🚨 You are ${formatMinutes(lateMinutes)} late and will be ${policy}.`, 'danger');
  }

  // Show shift info
  updateShiftInfo(shift);
}

function parseTime(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

function formatMinutes(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}h ${mins}m`;
  }
  return `${mins}m`;
}

function showValidationMessage(message, type) {
  const alertDiv = document.getElementById('validation-alert');
  if (alertDiv) {
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `<i class="bi bi-${getIconForType(type)} me-2"></i>${message}`;
    alertDiv.style.display = 'block';
  }
}

function getIconForType(type) {
  switch (type) {
    case 'success': return 'check-circle';
    case 'warning': return 'exclamation-triangle';
    case 'danger': return 'x-circle';
    case 'info': return 'info-circle';
    default: return 'info-circle';
  }
}

function updateShiftInfo(shift) {
  const shiftInfoDiv = document.getElementById('shift-info');
  if (shiftInfoDiv) {
    shiftInfoDiv.innerHTML = `
      <div class="card border-primary mb-3">
        <div class="card-body">
          <h6 class="card-title">Today's Shift: ${shift.name}</h6>
          <p class="card-text">
            <strong>Scheduled:</strong> ${formatTime(shift.start_time)} - ${formatTime(shift.end_time)}<br>
            <strong>Early Check-in:</strong> ${shift.early_checkin_allowed_hours}h allowed<br>
            <strong>Grace Period:</strong> ${shift.late_checkin_grace_hours}h after start time
          </p>
        </div>
      </div>
    `;
  }
}

function formatTime(timeString) {
  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
}

// Initialize validation when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Add validation elements to the page
  const clockSection = document.querySelector('.content-card');
  if (clockSection) {
    const validationHTML = `
      <div id="validation-alert" class="alert" style="display: none;" role="alert"></div>
      <div id="shift-info" class="mb-3"></div>
    `;
    clockSection.insertAdjacentHTML('afterbegin', validationHTML);
  }

  // Fetch initial data
  fetchShiftData();

  // Update every 30 seconds
  validationInterval = setInterval(fetchShiftData, 30000);
});

// Cleanup when page unloads
window.addEventListener('beforeunload', function() {
  if (stream) {
    stream.getTracks().forEach(track => track.stop());
  }
  if (validationInterval) {
    clearInterval(validationInterval);
  }
});
</script>
</body>
</html>
