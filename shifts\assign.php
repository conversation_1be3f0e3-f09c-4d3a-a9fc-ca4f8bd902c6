<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift assignment permissions
requirePermission($mysqli, 'shifts.assign');

$msg = "";
$error = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'assign_single') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $shift_date = $_POST['shift_date'] ?? '';

      if ($employee_id && $shift_template_id && $shift_date) {
        // Check if assignment already exists for this date
        $check_stmt = $mysqli->prepare("SELECT id FROM employee_shifts WHERE employee_id = ? AND ? BETWEEN shift_start_date AND shift_end_date");
        $check_stmt->bind_param('is', $employee_id, $shift_date);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
          $error = "Employee already has a shift assignment for this date. Please remove the existing assignment first.";
        } else {
          // Create new assignment (single date = start and end date are the same)
          $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_start_date, shift_end_date) VALUES (?, ?, ?, ?)");
          $stmt->bind_param('iiss', $employee_id, $shift_template_id, $shift_date, $shift_date);
          if ($stmt->execute()) {
            $msg = "Shift assigned successfully.";
          } else {
            $error = "Failed to assign shift.";
          }
          $stmt->close();
        }
        $check_stmt->close();
      } else {
        $error = "Please fill in all required fields.";
      }
    } elseif ($action === 'assign_range') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $start_date = $_POST['start_date'] ?? '';
      $end_date = $_POST['end_date'] ?? '';
      if ($employee_id && $shift_template_id && $start_date && $end_date) {
        // Check for overlapping assignments
        $check_stmt = $mysqli->prepare("
          SELECT id FROM employee_shifts
          WHERE employee_id = ?
          AND (
            (shift_start_date <= ? AND shift_end_date >= ?) OR
            (shift_start_date <= ? AND shift_end_date >= ?) OR
            (shift_start_date >= ? AND shift_end_date <= ?)
          )
        ");
        $check_stmt->bind_param('issssss', $employee_id, $start_date, $start_date, $end_date, $end_date, $start_date, $end_date);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
          $error = "Employee already has overlapping shift assignments in this date range. Please remove existing assignments first.";
        } else {
          // Create new assignment for the entire date range
          $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_start_date, shift_end_date) VALUES (?, ?, ?, ?)");
          $stmt->bind_param('iiss', $employee_id, $shift_template_id, $start_date, $end_date);
          if ($stmt->execute()) {
            $msg = "Shift assigned successfully for date range: " . date('M j', strtotime($start_date)) . " - " . date('M j, Y', strtotime($end_date));
          } else {
            $error = "Failed to assign shift for date range.";
          }
          $stmt->close();
        }
        $check_stmt->close();
      } else {
        $error = "Please fill in all required fields.";
      }
    } elseif ($action === 'assign_multiple') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $selected_dates = $_POST['selected_dates'] ?? '';

      if ($employee_id && $shift_template_id && $selected_dates) {
        // Parse the selected dates (comma-separated)
        $dates_array = array_filter(array_map('trim', explode(',', $selected_dates)));

        if (empty($dates_array)) {
          $error = "Please select at least one date.";
        } else {
          $conflicts = [];
          $successful_assignments = 0;

          // Check each date for conflicts
          foreach ($dates_array as $date) {
            $check_stmt = $mysqli->prepare("SELECT id FROM employee_shifts WHERE employee_id = ? AND ? BETWEEN shift_start_date AND shift_end_date");
            $check_stmt->bind_param('is', $employee_id, $date);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
              $conflicts[] = date('M j, Y', strtotime($date));
            } else {
              // Create assignment for this date
              $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_start_date, shift_end_date) VALUES (?, ?, ?, ?)");
              $stmt->bind_param('iiss', $employee_id, $shift_template_id, $date, $date);
              if ($stmt->execute()) {
                $successful_assignments++;
              }
              $stmt->close();
            }
            $check_stmt->close();
          }

          if (!empty($conflicts)) {
            $error = "Some dates could not be assigned due to existing shifts: " . implode(', ', $conflicts);
            if ($successful_assignments > 0) {
              $error .= ". However, $successful_assignments shifts were assigned successfully.";
            }
          } else {
            $msg = "Successfully assigned shifts for $successful_assignments dates.";
          }
        }
      } else {
        $error = "Please fill in all required fields and select at least one date.";
      }
    }
  }
}

// Get pre-selected employee ID from URL parameter
$preselected_employee_id = intval($_GET['employee_id'] ?? 0);
$preselected_employee_name = '';

// Fetch employees
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);

// Get the name of the preselected employee for display
if ($preselected_employee_id) {
  foreach ($employees as $employee) {
    if ($employee['id'] == $preselected_employee_id) {
      $preselected_employee_name = $employee['name'];
      break;
    }
  }
}

// Fetch shift templates
$templates_result = $mysqli->query("SELECT id, name, start_time, end_time, description FROM shift_templates ORDER BY name");
$templates = $templates_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Assign Shifts - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
  <style>
    /* Compact Calendar Styles */
    .calendar-container {
      max-width: 600px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 1.5rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin-bottom: 0;
    }

    .calendar-header h5 {
      margin: 0;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .calendar-header .btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
    }

    .calendar-header .btn:hover {
      background: rgba(255,255,255,0.3);
      border-color: rgba(255,255,255,0.4);
      color: white;
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      background: #e9ecef;
      padding: 1px;
    }

    .calendar-weekday {
      font-weight: 600;
      text-align: center;
      padding: 12px 8px;
      background: #f8f9fa;
      color: #495057;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .calendar-day {
      aspect-ratio: 1;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.9rem;
      font-weight: 500;
      position: relative;
      min-height: 40px;
    }

    .calendar-day:hover:not(.disabled) {
      background: #e3f2fd;
      color: #1976d2;
      transform: scale(1.05);
    }

    .calendar-day.selected {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .calendar-day.selected:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .calendar-day.disabled {
      background: #f8f9fa;
      color: #adb5bd;
      cursor: not-allowed;
    }

    .calendar-day.other-month {
      background: #fafafa;
      color: #dee2e6;
    }

    .calendar-day.today:not(.selected) {
      background: #fff3cd;
      color: #856404;
      font-weight: 600;
    }

    /* Selected dates counter */
    .selected-counter {
      position: absolute;
      top: -8px;
      right: -8px;
      background: #dc3545;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 0.7rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .calendar-container {
        max-width: 100%;
        margin: 0;
        border-radius: 8px;
      }

      .calendar-header {
        padding: 0.75rem 1rem;
      }

      .calendar-header h5 {
        font-size: 1rem;
      }

      .calendar-day {
        font-size: 0.8rem;
        min-height: 36px;
      }

      .calendar-weekday {
        padding: 8px 4px;
        font-size: 0.7rem;
      }
    }

    @media (max-width: 576px) {
      .calendar-day {
        font-size: 0.75rem;
        min-height: 32px;
      }

      .calendar-weekday {
        padding: 6px 2px;
        font-size: 0.65rem;
      }
    }
  </style>
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Pre-selected Employee Notification -->
    <?php if ($preselected_employee_id && $preselected_employee_name): ?>
      <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        <strong><?= htmlspecialchars($preselected_employee_name) ?></strong> has been automatically selected in all assignment forms below. You can change the selection if needed.
      </div>
    <?php endif; ?>

    <!-- Assignment Tabs -->
    <div class="content-card">
      <ul class="nav nav-tabs" id="assignmentTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="single-tab" data-bs-toggle="tab" data-bs-target="#single" type="button" role="tab">
            <i class="bi bi-calendar-plus me-2"></i>
            Single Date Assignment
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="range-tab" data-bs-toggle="tab" data-bs-target="#range" type="button" role="tab">
            <i class="bi bi-calendar-range me-2"></i>
            Date Range Assignment
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="multiple-tab" data-bs-toggle="tab" data-bs-target="#multiple" type="button" role="tab">
            <i class="bi bi-calendar-check me-2"></i>
            Multiple Dates Assignment
          </button>
        </li>
      </ul>

      <div class="tab-content" id="assignmentTabsContent">
        <!-- Single Date Assignment -->
        <div class="tab-pane fade show active" id="single" role="tabpanel">
          <div class="p-4">

            <form method="POST" class="row g-3">
              <input type="hidden" name="action" value="assign_single" />

              <div class="col-md-6">
                <label for="employee_id" class="form-label">Employee</label>
                <select class="form-select" id="employee_id" name="employee_id" required>
                  <option value="">Select Employee</option>
                  <?php foreach ($employees as $employee): ?>
                    <option value="<?= $employee['id'] ?>" <?= ($employee['id'] == $preselected_employee_id) ? 'selected' : '' ?>>
                      <?= htmlspecialchars($employee['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="col-md-6">
                <label for="shift_template_id" class="form-label">Shift Template</label>
                <select class="form-select" id="shift_template_id" name="shift_template_id" required>
                  <option value="">Select Shift Template</option>
                  <?php foreach ($templates as $template): ?>
                    <option value="<?= $template['id'] ?>"
                            data-start="<?= $template['start_time'] ?>"
                            data-end="<?= $template['end_time'] ?>"
                            data-description="<?= htmlspecialchars($template['description']) ?>">
                      <?= htmlspecialchars($template['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="col-md-6">
                <label for="shift_date" class="form-label">Date</label>
                <input type="date" class="form-control" id="shift_date" name="shift_date" required
                       min="<?= date('Y-m-d') ?>" />
              </div>

              <div class="col-md-6">
                <label class="form-label">Shift Details</label>
                <div id="shift_details" class="form-control-plaintext text-muted">
                  Select a shift template to see details
                </div>
              </div>

              <div class="col-12">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-check-circle me-1"></i>
                  Assign Shift
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Date Range Assignment -->
        <div class="tab-pane fade" id="range" role="tabpanel">
          <div class="p-4">

            <form method="POST" class="row g-3">
              <input type="hidden" name="action" value="assign_range" />

              <div class="col-md-6">
                <label for="range_employee_id" class="form-label">Employee</label>
                <select class="form-select" id="range_employee_id" name="employee_id" required>
                  <option value="">Select Employee</option>
                  <?php foreach ($employees as $employee): ?>
                    <option value="<?= $employee['id'] ?>" <?= ($employee['id'] == $preselected_employee_id) ? 'selected' : '' ?>>
                      <?= htmlspecialchars($employee['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="col-md-6">
                <label for="range_shift_template_id" class="form-label">Shift Template</label>
                <select class="form-select" id="range_shift_template_id" name="shift_template_id" required>
                  <option value="">Select Shift Template</option>
                  <?php foreach ($templates as $template): ?>
                    <option value="<?= $template['id'] ?>">
                      <?= htmlspecialchars($template['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="col-md-6">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" required
                       min="<?= date('Y-m-d') ?>" />
              </div>

              <div class="col-md-6">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" required
                       min="<?= date('Y-m-d') ?>" />
              </div>



              <div class="col-12">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-calendar-check me-1"></i>
                  Assign Shifts for Range
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Multiple Dates Assignment -->
        <div class="tab-pane fade" id="multiple" role="tabpanel">
          <div class="p-4">

            <form method="POST" class="row g-3">
              <input type="hidden" name="action" value="assign_multiple" />
              <input type="hidden" name="selected_dates" id="selected_dates" />

              <div class="col-md-6">
                <label for="multiple_employee_id" class="form-label">Employee</label>
                <select class="form-select" id="multiple_employee_id" name="employee_id" required>
                  <option value="">Select Employee</option>
                  <?php foreach ($employees as $employee): ?>
                    <option value="<?= $employee['id'] ?>" <?= ($employee['id'] == $preselected_employee_id) ? 'selected' : '' ?>>
                      <?= htmlspecialchars($employee['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="col-md-6">
                <label for="multiple_shift_template_id" class="form-label">Shift Template</label>
                <select class="form-select" id="multiple_shift_template_id" name="shift_template_id" required>
                  <option value="">Select Shift Template</option>
                  <?php foreach ($templates as $template): ?>
                    <option value="<?= $template['id'] ?>">
                      <?= htmlspecialchars($template['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="col-12">
                <label class="form-label">Select Dates</label>
                <div class="calendar-container">
                  <div id="calendar"></div>
                </div>
                <small class="form-text text-muted mt-2">Click on dates to select/deselect them. Selected dates will be highlighted in purple.</small>
              </div>

              <div class="col-12">
                <label class="form-label">Selected Dates</label>
                <div id="selected_dates_display" class="form-control-plaintext text-muted">
                  No dates selected
                </div>
              </div>

              <div class="col-12">
                <button type="submit" class="btn btn-primary" id="assign_multiple_btn" disabled>
                  <i class="bi bi-calendar-check me-1"></i>
                  Assign Shifts for Selected Dates
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>


  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Update shift details when template is selected
    document.getElementById('shift_template_id').addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];
      const detailsDiv = document.getElementById('shift_details');

      if (selectedOption.value) {
        const startTime = selectedOption.dataset.start;
        const endTime = selectedOption.dataset.end;
        const description = selectedOption.dataset.description;

        const startFormatted = new Date('2000-01-01 ' + startTime).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        const endFormatted = new Date('2000-01-01 ' + endTime).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });

        detailsDiv.innerHTML = `
          <strong>Time:</strong> ${startFormatted} - ${endFormatted}<br>
          ${description ? '<strong>Description:</strong> ' + description : ''}
        `;
        detailsDiv.className = 'form-control-plaintext text-info';
      } else {
        detailsDiv.innerHTML = 'Select a shift template to see details';
        detailsDiv.className = 'form-control-plaintext text-muted';
      }
    });

    // Set minimum end date based on start date
    document.getElementById('start_date').addEventListener('change', function() {
      document.getElementById('end_date').min = this.value;
    });

    // Calendar functionality for multiple dates selection
    class MultiDateCalendar {
      constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.selectedDates = new Set();
        this.currentDate = new Date();
        this.currentDate.setDate(1); // Start at first day of current month
        this.today = new Date();
        this.init();
      }

      init() {
        this.render();
      }

      render() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();

        // Create header
        const header = document.createElement('div');
        header.className = 'calendar-header';
        header.innerHTML = `
          <button type="button" class="btn" onclick="calendar.previousMonth()">
            <i class="bi bi-chevron-left"></i>
          </button>
          <h5>${this.getMonthName(month)} ${year}</h5>
          <button type="button" class="btn" onclick="calendar.nextMonth()">
            <i class="bi bi-chevron-right"></i>
          </button>
        `;

        // Create calendar grid
        const grid = document.createElement('div');
        grid.className = 'calendar-grid';

        // Add weekday headers
        const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        weekdays.forEach(day => {
          const dayHeader = document.createElement('div');
          dayHeader.className = 'calendar-weekday';
          dayHeader.textContent = day;
          grid.appendChild(dayHeader);
        });

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Add empty cells for days before month starts
        for (let i = 0; i < startingDayOfWeek; i++) {
          const emptyDay = document.createElement('div');
          emptyDay.className = 'calendar-day other-month';
          grid.appendChild(emptyDay);
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
          const dayElement = document.createElement('div');
          dayElement.className = 'calendar-day';
          dayElement.textContent = day;

          const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
          const dateObj = new Date(year, month, day);
          const todayStr = this.today.toISOString().split('T')[0];

          // Check if this is today
          if (dateStr === todayStr) {
            dayElement.classList.add('today');
          }

          // Disable past dates
          if (dateObj < this.today.setHours(0, 0, 0, 0)) {
            dayElement.classList.add('disabled');
          } else {
            dayElement.addEventListener('click', () => this.toggleDate(dateStr, dayElement));
          }

          // Mark selected dates
          if (this.selectedDates.has(dateStr)) {
            dayElement.classList.add('selected');
          }

          grid.appendChild(dayElement);
        }

        // Clear container and add new content
        this.container.innerHTML = '';
        this.container.appendChild(header);
        this.container.appendChild(grid);
      }

      toggleDate(dateStr, element) {
        if (element.classList.contains('disabled')) return;

        if (this.selectedDates.has(dateStr)) {
          this.selectedDates.delete(dateStr);
          element.classList.remove('selected');
        } else {
          this.selectedDates.add(dateStr);
          element.classList.add('selected');
        }

        this.updateSelectedDatesDisplay();
      }

      updateSelectedDatesDisplay() {
        const displayElement = document.getElementById('selected_dates_display');
        const hiddenInput = document.getElementById('selected_dates');
        const submitButton = document.getElementById('assign_multiple_btn');

        if (this.selectedDates.size === 0) {
          displayElement.textContent = 'No dates selected';
          displayElement.className = 'form-control-plaintext text-muted';
          hiddenInput.value = '';
          submitButton.disabled = true;
        } else {
          const sortedDates = Array.from(this.selectedDates).sort();
          const formattedDates = sortedDates.map(date => {
            const d = new Date(date + 'T00:00:00');
            return d.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            });
          });

          displayElement.innerHTML = `<strong>${this.selectedDates.size} dates selected:</strong><br>${formattedDates.join(', ')}`;
          displayElement.className = 'form-control-plaintext text-info';
          hiddenInput.value = sortedDates.join(',');
          submitButton.disabled = false;
        }
      }

      previousMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        this.render();
      }

      nextMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        this.render();
      }

      getMonthName(monthIndex) {
        const months = [
          'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];
        return months[monthIndex];
      }
    }

    // Initialize calendar when the multiple dates tab is shown
    let calendar;
    document.getElementById('multiple-tab').addEventListener('shown.bs.tab', function() {
      if (!calendar) {
        calendar = new MultiDateCalendar('calendar');
      }
    });
  </script>
</body>
</html>
