<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit;
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

// Check if user has permission to view reports
if (!hasPermission($mysqli, $_SESSION['user_id'], 'reports.view')) {
    header("Location: ../dashboard.php?error=" . urlencode("You don't have permission to view reports."));
    exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

// Get filter parameters
$report_type = $_GET['report_type'] ?? 'daily_summary';
$employee_filter = intval($_GET['employee_id'] ?? 0);
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');
$department_filter = $_GET['department'] ?? '';

// Fetch employees for filter
$employees_result = $mysqli->query("SELECT id, name, department FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);

// Get unique departments
$departments = array_unique(array_column($employees, 'department'));
sort($departments);

// Generate report data based on type
$report_data = [];
$report_title = '';

switch ($report_type) {
    case 'daily_summary':
        $report_title = 'Daily Attendance Summary';
        $report_data = generateDailySummaryReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter);
        break;
    
    case 'overtime_report':
        $report_title = 'Overtime Hours Report';
        $report_data = generateOvertimeReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter);
        break;
    
    case 'lateness_report':
        $report_title = 'Lateness Analysis Report';
        $report_data = generateLatenessReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter);
        break;
    
    case 'attendance_trends':
        $report_title = 'Attendance Trends Analysis';
        $report_data = generateTrendsReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter);
        break;
}

function generateDailySummaryReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter) {
    $where_conditions = ["DATE(a.timestamp) BETWEEN ? AND ?"];
    $params = [$start_date, $end_date];
    $param_types = 'ss';
    
    if ($employee_filter) {
        $where_conditions[] = "a.employee_id = ?";
        $params[] = $employee_filter;
        $param_types .= 'i';
    }
    
    if ($department_filter) {
        $where_conditions[] = "e.department = ?";
        $params[] = $department_filter;
        $param_types .= 's';
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $mysqli->prepare("
        SELECT 
            DATE(a.timestamp) as date,
            e.name as employee_name,
            e.department,
            COUNT(CASE WHEN a.type = 'in' THEN 1 END) as check_ins,
            COUNT(CASE WHEN a.type = 'out' THEN 1 END) as check_outs,
            SUM(a.overtime_hours) as total_overtime,
            SUM(a.late_hours) as total_late_hours,
            SUM(a.worked_hours) as total_worked_hours,
            COUNT(CASE WHEN a.attendance_status = 'late' THEN 1 END) as late_instances,
            COUNT(CASE WHEN a.attendance_status = 'absent' THEN 1 END) as absent_instances
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        WHERE $where_clause
        GROUP BY DATE(a.timestamp), a.employee_id
        ORDER BY DATE(a.timestamp) DESC, e.name
    ");
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

function generateOvertimeReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter) {
    $where_conditions = ["DATE(a.timestamp) BETWEEN ? AND ?", "a.overtime_hours > 0"];
    $params = [$start_date, $end_date];
    $param_types = 'ss';
    
    if ($employee_filter) {
        $where_conditions[] = "a.employee_id = ?";
        $params[] = $employee_filter;
        $param_types .= 'i';
    }
    
    if ($department_filter) {
        $where_conditions[] = "e.department = ?";
        $params[] = $department_filter;
        $param_types .= 's';
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $mysqli->prepare("
        SELECT 
            e.name as employee_name,
            e.department,
            DATE(a.timestamp) as date,
            a.type,
            a.overtime_hours,
            a.is_weekend,
            a.is_holiday,
            st.name as shift_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN shift_templates st ON a.shift_template_id = st.id
        WHERE $where_clause
        ORDER BY a.overtime_hours DESC, DATE(a.timestamp) DESC
    ");
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

function generateLatenessReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter) {
    $where_conditions = ["DATE(a.timestamp) BETWEEN ? AND ?", "a.late_hours > 0"];
    $params = [$start_date, $end_date];
    $param_types = 'ss';
    
    if ($employee_filter) {
        $where_conditions[] = "a.employee_id = ?";
        $params[] = $employee_filter;
        $param_types .= 'i';
    }
    
    if ($department_filter) {
        $where_conditions[] = "e.department = ?";
        $params[] = $department_filter;
        $param_types .= 's';
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $mysqli->prepare("
        SELECT 
            e.name as employee_name,
            e.department,
            DATE(a.timestamp) as date,
            a.late_hours,
            a.attendance_status,
            TIME(a.timestamp) as actual_time,
            a.scheduled_start_time,
            st.name as shift_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN shift_templates st ON a.shift_template_id = st.id
        WHERE $where_clause
        ORDER BY a.late_hours DESC, DATE(a.timestamp) DESC
    ");
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

function generateTrendsReport($mysqli, $start_date, $end_date, $employee_filter, $department_filter) {
    $where_conditions = ["DATE(a.timestamp) BETWEEN ? AND ?"];
    $params = [$start_date, $end_date];
    $param_types = 'ss';
    
    if ($employee_filter) {
        $where_conditions[] = "a.employee_id = ?";
        $params[] = $employee_filter;
        $param_types .= 'i';
    }
    
    if ($department_filter) {
        $where_conditions[] = "e.department = ?";
        $params[] = $department_filter;
        $param_types .= 's';
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $mysqli->prepare("
        SELECT 
            DATE(a.timestamp) as date,
            COUNT(DISTINCT a.employee_id) as unique_employees,
            AVG(a.overtime_hours) as avg_overtime,
            AVG(a.late_hours) as avg_lateness,
            AVG(a.worked_hours) as avg_worked_hours,
            COUNT(CASE WHEN a.attendance_status = 'on_time' THEN 1 END) as on_time_count,
            COUNT(CASE WHEN a.attendance_status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN a.attendance_status = 'absent' THEN 1 END) as absent_count,
            COUNT(*) as total_records
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        WHERE $where_clause
        GROUP BY DATE(a.timestamp)
        ORDER BY DATE(a.timestamp) DESC
    ");
    
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Attendance Reports - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Attendance Reports</h1>
          <p class="page-subtitle">Comprehensive attendance analytics and insights</p>
        </div>
        <div class="d-flex gap-3">
          <button onclick="exportReport()" class="btn btn-success">
            <i class="bi bi-download me-2"></i>
            Export Report
          </button>
          <a href="../dashboard.php" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Report Filters -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-funnel me-2"></i>
        Report Configuration
      </h2>
      <form method="GET" class="row g-3">
        <div class="col-md-3">
          <label for="report_type" class="form-label">Report Type</label>
          <select class="form-select" id="report_type" name="report_type">
            <option value="daily_summary" <?= $report_type === 'daily_summary' ? 'selected' : '' ?>>Daily Summary</option>
            <option value="overtime_report" <?= $report_type === 'overtime_report' ? 'selected' : '' ?>>Overtime Report</option>
            <option value="lateness_report" <?= $report_type === 'lateness_report' ? 'selected' : '' ?>>Lateness Analysis</option>
            <option value="attendance_trends" <?= $report_type === 'attendance_trends' ? 'selected' : '' ?>>Attendance Trends</option>
          </select>
        </div>

        <div class="col-md-3">
          <label for="employee_id" class="form-label">Employee</label>
          <select class="form-select" id="employee_id" name="employee_id">
            <option value="">All Employees</option>
            <?php foreach ($employees as $employee): ?>
              <option value="<?= $employee['id'] ?>" <?= $employee_filter == $employee['id'] ? 'selected' : '' ?>>
                <?= htmlspecialchars($employee['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>

        <div class="col-md-2">
          <label for="department" class="form-label">Department</label>
          <select class="form-select" id="department" name="department">
            <option value="">All Departments</option>
            <?php foreach ($departments as $dept): ?>
              <option value="<?= htmlspecialchars($dept) ?>" <?= $department_filter === $dept ? 'selected' : '' ?>>
                <?= htmlspecialchars($dept) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>

        <div class="col-md-2">
          <label for="start_date" class="form-label">Start Date</label>
          <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($start_date) ?>" />
        </div>

        <div class="col-md-2">
          <label for="end_date" class="form-label">End Date</label>
          <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($end_date) ?>" />
        </div>

        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-bar-chart me-1"></i>
            Generate Report
          </button>
        </div>
      </form>
    </div>

    <!-- Report Results -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-graph-up me-2"></i>
        <?= htmlspecialchars($report_title) ?>
      </h2>
      <p class="text-muted">
        Report Period: <?= date('F j, Y', strtotime($start_date)) ?> to <?= date('F j, Y', strtotime($end_date)) ?>
        <?php if ($employee_filter): ?>
          | Employee: <?= htmlspecialchars($employees[array_search($employee_filter, array_column($employees, 'id'))]['name'] ?? 'Unknown') ?>
        <?php endif; ?>
        <?php if ($department_filter): ?>
          | Department: <?= htmlspecialchars($department_filter) ?>
        <?php endif; ?>
      </p>

      <?php if (empty($report_data)): ?>
        <div class="text-center py-4">
          <i class="bi bi-graph-down display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Data Found</h4>
          <p class="text-muted">No attendance data found for the selected criteria and date range.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table table-hover" id="reportTable">
            <thead>
              <tr>
                <?php if ($report_type === 'daily_summary'): ?>
                  <th>Date</th>
                  <th>Employee</th>
                  <th>Department</th>
                  <th>Check-ins</th>
                  <th>Check-outs</th>
                  <th>Worked Hours</th>
                  <th>Overtime</th>
                  <th>Late Hours</th>
                  <th>Late Instances</th>
                <?php elseif ($report_type === 'overtime_report'): ?>
                  <th>Employee</th>
                  <th>Department</th>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Overtime Hours</th>
                  <th>Weekend</th>
                  <th>Holiday</th>
                  <th>Shift</th>
                <?php elseif ($report_type === 'lateness_report'): ?>
                  <th>Employee</th>
                  <th>Department</th>
                  <th>Date</th>
                  <th>Scheduled Time</th>
                  <th>Actual Time</th>
                  <th>Late Hours</th>
                  <th>Status</th>
                  <th>Shift</th>
                <?php elseif ($report_type === 'attendance_trends'): ?>
                  <th>Date</th>
                  <th>Employees</th>
                  <th>Avg Worked Hours</th>
                  <th>Avg Overtime</th>
                  <th>Avg Lateness</th>
                  <th>On Time</th>
                  <th>Late</th>
                  <th>Absent</th>
                  <th>Total Records</th>
                <?php endif; ?>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($report_data as $row): ?>
              <tr>
                <?php if ($report_type === 'daily_summary'): ?>
                  <td><?= date('M j, Y', strtotime($row['date'])) ?></td>
                  <td><?= htmlspecialchars($row['employee_name']) ?></td>
                  <td><?= htmlspecialchars($row['department']) ?></td>
                  <td><?= $row['check_ins'] ?></td>
                  <td><?= $row['check_outs'] ?></td>
                  <td><?= number_format($row['total_worked_hours'], 2) ?>h</td>
                  <td><?= number_format($row['total_overtime'], 2) ?>h</td>
                  <td><?= number_format($row['total_late_hours'], 2) ?>h</td>
                  <td><?= $row['late_instances'] ?></td>
                <?php elseif ($report_type === 'overtime_report'): ?>
                  <td><?= htmlspecialchars($row['employee_name']) ?></td>
                  <td><?= htmlspecialchars($row['department']) ?></td>
                  <td><?= date('M j, Y', strtotime($row['date'])) ?></td>
                  <td><span class="badge bg-<?= $row['type'] === 'in' ? 'success' : 'danger' ?>"><?= ucfirst($row['type']) ?></span></td>
                  <td><?= number_format($row['overtime_hours'], 2) ?>h</td>
                  <td><?= $row['is_weekend'] ? '<span class="badge bg-info">Yes</span>' : 'No' ?></td>
                  <td><?= $row['is_holiday'] ? '<span class="badge bg-warning">Yes</span>' : 'No' ?></td>
                  <td><?= htmlspecialchars($row['shift_name'] ?: 'N/A') ?></td>
                <?php elseif ($report_type === 'lateness_report'): ?>
                  <td><?= htmlspecialchars($row['employee_name']) ?></td>
                  <td><?= htmlspecialchars($row['department']) ?></td>
                  <td><?= date('M j, Y', strtotime($row['date'])) ?></td>
                  <td><?= $row['scheduled_start_time'] ? date('g:i A', strtotime($row['scheduled_start_time'])) : 'N/A' ?></td>
                  <td><?= date('g:i A', strtotime($row['actual_time'])) ?></td>
                  <td><?= number_format($row['late_hours'], 2) ?>h</td>
                  <td><span class="badge bg-warning"><?= ucwords(str_replace('_', ' ', $row['attendance_status'])) ?></span></td>
                  <td><?= htmlspecialchars($row['shift_name'] ?: 'N/A') ?></td>
                <?php elseif ($report_type === 'attendance_trends'): ?>
                  <td><?= date('M j, Y', strtotime($row['date'])) ?></td>
                  <td><?= $row['unique_employees'] ?></td>
                  <td><?= number_format($row['avg_worked_hours'], 2) ?>h</td>
                  <td><?= number_format($row['avg_overtime'], 2) ?>h</td>
                  <td><?= number_format($row['avg_lateness'], 2) ?>h</td>
                  <td><span class="badge bg-success"><?= $row['on_time_count'] ?></span></td>
                  <td><span class="badge bg-warning"><?= $row['late_count'] ?></span></td>
                  <td><span class="badge bg-danger"><?= $row['absent_count'] ?></span></td>
                  <td><?= $row['total_records'] ?></td>
                <?php endif; ?>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function exportReport() {
      // Simple CSV export functionality
      const table = document.getElementById('reportTable');
      if (!table) return;
      
      let csv = [];
      const rows = table.querySelectorAll('tr');
      
      for (let i = 0; i < rows.length; i++) {
        const row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
          // Clean up the text content
          let cellText = cols[j].textContent.trim();
          cellText = cellText.replace(/"/g, '""'); // Escape quotes
          row.push('"' + cellText + '"');
        }
        
        csv.push(row.join(','));
      }
      
      // Download CSV
      const csvContent = csv.join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.setAttribute('hidden', '');
      a.setAttribute('href', url);
      a.setAttribute('download', 'attendance_report_' + new Date().toISOString().split('T')[0] + '.csv');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  </script>
</body>
</html>
