<?php
/**
 * Notification System for Attendance Policy Violations
 * Version 2.0.0
 * 
 * Handles creation, management, and display of attendance-related notifications
 */

/**
 * Create a notification for attendance policy violations
 * 
 * @param mysqli $mysqli Database connection
 * @param int $employee_id Employee ID
 * @param string $type Notification type (late_arrival, excessive_overtime, absence, etc.)
 * @param string $message Notification message
 * @param array $data Additional data (attendance_id, violation_details, etc.)
 * @return bool Success status
 */
function createAttendanceNotification($mysqli, $employee_id, $type, $message, $data = []) {
    // Create notifications table if it doesn't exist
    createNotificationsTable($mysqli);
    
    $severity = determineNotificationSeverity($type);
    $data_json = json_encode($data);
    
    $stmt = $mysqli->prepare("
        INSERT INTO notifications (
            employee_id, type, severity, message, data, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->bind_param('issss', $employee_id, $type, $severity, $message, $data_json);
    $success = $stmt->execute();
    $stmt->close();
    
    // Also notify managers if it's a high severity violation
    if ($success && in_array($severity, ['high', 'critical'])) {
        notifyManagers($mysqli, $employee_id, $type, $message, $data);
    }
    
    return $success;
}

/**
 * Create notifications table if it doesn't exist
 */
function createNotificationsTable($mysqli) {
    $sql = "
        CREATE TABLE IF NOT EXISTS `notifications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `employee_id` int(11) NOT NULL,
            `type` varchar(50) NOT NULL,
            `severity` enum('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
            `message` text NOT NULL,
            `data` json NULL,
            `is_read` boolean NOT NULL DEFAULT FALSE,
            `is_acknowledged` boolean NOT NULL DEFAULT FALSE,
            `acknowledged_by` int(11) NULL,
            `acknowledged_at` timestamp NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_employee_notifications` (`employee_id`, `created_at`),
            KEY `idx_notification_type` (`type`),
            KEY `idx_notification_severity` (`severity`),
            FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`acknowledged_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $mysqli->query($sql);
}

/**
 * Determine notification severity based on type
 */
function determineNotificationSeverity($type) {
    $severity_map = [
        'late_arrival' => 'medium',
        'very_late_arrival' => 'high',
        'excessive_lateness' => 'critical',
        'absence' => 'high',
        'excessive_overtime' => 'medium',
        'early_departure' => 'medium',
        'missed_checkout' => 'high',
        'policy_violation' => 'high',
        'attendance_pattern' => 'medium',
        'shift_violation' => 'high'
    ];
    
    return $severity_map[$type] ?? 'medium';
}

/**
 * Notify managers about high-severity violations
 */
function notifyManagers($mysqli, $employee_id, $type, $message, $data) {
    // Get employee details
    $emp_stmt = $mysqli->prepare("SELECT name, department FROM employees WHERE id = ?");
    $emp_stmt->bind_param('i', $employee_id);
    $emp_stmt->execute();
    $emp_stmt->bind_result($employee_name, $department);
    $emp_stmt->fetch();
    $emp_stmt->close();
    
    // Get managers who should be notified
    $manager_stmt = $mysqli->query("
        SELECT DISTINCT u.id as user_id, e.id as employee_id
        FROM users u
        JOIN user_roles ur ON u.id = ur.user_id
        JOIN roles r ON ur.role_id = r.id
        LEFT JOIN employees e ON u.employee_id = e.id
        WHERE r.name IN ('shift_manager', 'admin', 'super_admin')
    ");
    
    $manager_message = "ALERT: {$employee_name} ({$department}) - {$message}";
    
    while ($manager = $manager_stmt->fetch_assoc()) {
        if ($manager['employee_id']) {
            createAttendanceNotification(
                $mysqli, 
                $manager['employee_id'], 
                'manager_alert', 
                $manager_message,
                array_merge($data, [
                    'original_employee_id' => $employee_id,
                    'original_employee_name' => $employee_name,
                    'original_violation_type' => $type
                ])
            );
        }
    }
}

/**
 * Process attendance record and create notifications for violations
 */
function processAttendanceNotifications($mysqli, $attendance_id, $employee_id, $attendance_data) {
    $notifications = [];
    
    // Late arrival notifications
    if ($attendance_data['late_hours'] > 0) {
        if ($attendance_data['late_hours'] >= 2.0) {
            $notifications[] = [
                'type' => 'excessive_lateness',
                'message' => "Excessive lateness: {$attendance_data['late_hours']} hours late"
            ];
        } elseif ($attendance_data['attendance_status'] === 'very_late') {
            $notifications[] = [
                'type' => 'very_late_arrival',
                'message' => "Very late arrival: {$attendance_data['late_hours']} hours beyond grace period"
            ];
        } else {
            $notifications[] = [
                'type' => 'late_arrival',
                'message' => "Late arrival: {$attendance_data['late_hours']} hours late"
            ];
        }
    }
    
    // Absence notifications
    if ($attendance_data['attendance_status'] === 'absent') {
        $notifications[] = [
            'type' => 'absence',
            'message' => "Marked as absent due to late arrival beyond policy limits"
        ];
    }
    
    // Excessive overtime notifications
    if ($attendance_data['overtime_hours'] >= 4.0) {
        $notifications[] = [
            'type' => 'excessive_overtime',
            'message' => "Excessive overtime: {$attendance_data['overtime_hours']} hours"
        ];
    }
    
    // Early departure notifications
    if (isset($attendance_data['early_departure_hours']) && $attendance_data['early_departure_hours'] > 0) {
        $notifications[] = [
            'type' => 'early_departure',
            'message' => "Early departure: {$attendance_data['early_departure_hours']} hours before scheduled end"
        ];
    }
    
    // Create all notifications
    foreach ($notifications as $notification) {
        createAttendanceNotification(
            $mysqli,
            $employee_id,
            $notification['type'],
            $notification['message'],
            [
                'attendance_id' => $attendance_id,
                'attendance_data' => $attendance_data,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        );
    }
    
    return count($notifications);
}

/**
 * Get unread notifications for an employee
 */
function getUnreadNotifications($mysqli, $employee_id, $limit = 10) {
    $stmt = $mysqli->prepare("
        SELECT * FROM notifications 
        WHERE employee_id = ? AND is_read = FALSE 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->bind_param('ii', $employee_id, $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    $notifications = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    return $notifications;
}

/**
 * Mark notification as read
 */
function markNotificationAsRead($mysqli, $notification_id, $user_id = null) {
    $stmt = $mysqli->prepare("
        UPDATE notifications 
        SET is_read = TRUE, read_at = NOW() 
        WHERE id = ?
    ");
    $stmt->bind_param('i', $notification_id);
    $success = $stmt->execute();
    $stmt->close();
    
    return $success;
}

/**
 * Acknowledge notification (for managers)
 */
function acknowledgeNotification($mysqli, $notification_id, $user_id) {
    $stmt = $mysqli->prepare("
        UPDATE notifications 
        SET is_acknowledged = TRUE, acknowledged_by = ?, acknowledged_at = NOW() 
        WHERE id = ?
    ");
    $stmt->bind_param('ii', $user_id, $notification_id);
    $success = $stmt->execute();
    $stmt->close();
    
    return $success;
}

/**
 * Get notification statistics for dashboard
 */
function getNotificationStats($mysqli, $employee_id) {
    $stmt = $mysqli->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread,
            COUNT(CASE WHEN severity = 'high' OR severity = 'critical' THEN 1 END) as high_priority,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
        FROM notifications 
        WHERE employee_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->bind_param('i', $employee_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stats = $result->fetch_assoc();
    $stmt->close();
    
    return $stats;
}

/**
 * Clean up old notifications (older than 90 days)
 */
function cleanupOldNotifications($mysqli) {
    $stmt = $mysqli->prepare("
        DELETE FROM notifications 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
    ");
    $success = $stmt->execute();
    $affected_rows = $stmt->affected_rows;
    $stmt->close();
    
    return ['success' => $success, 'deleted_count' => $affected_rows];
}

/**
 * Get attendance pattern violations for an employee
 */
function checkAttendancePatterns($mysqli, $employee_id, $days = 30) {
    $stmt = $mysqli->prepare("
        SELECT 
            COUNT(CASE WHEN attendance_status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN attendance_status = 'very_late' THEN 1 END) as very_late_count,
            COUNT(CASE WHEN attendance_status = 'absent' THEN 1 END) as absent_count,
            COUNT(*) as total_days,
            AVG(late_hours) as avg_late_hours,
            AVG(overtime_hours) as avg_overtime_hours
        FROM attendance 
        WHERE employee_id = ? 
        AND DATE(timestamp) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
        AND type = 'in'
    ");
    $stmt->bind_param('ii', $employee_id, $days);
    $stmt->execute();
    $result = $stmt->get_result();
    $pattern = $result->fetch_assoc();
    $stmt->close();
    
    // Check for concerning patterns
    $violations = [];
    
    if ($pattern['late_count'] >= 5) {
        $violations[] = "Frequent lateness: {$pattern['late_count']} late arrivals in {$days} days";
    }
    
    if ($pattern['absent_count'] >= 3) {
        $violations[] = "Excessive absences: {$pattern['absent_count']} absences in {$days} days";
    }
    
    if ($pattern['avg_late_hours'] > 0.5) {
        $violations[] = "Consistent lateness pattern: Average " . number_format($pattern['avg_late_hours'], 2) . " hours late";
    }
    
    // Create pattern violation notifications
    foreach ($violations as $violation) {
        createAttendanceNotification(
            $mysqli,
            $employee_id,
            'attendance_pattern',
            $violation,
            [
                'pattern_data' => $pattern,
                'analysis_period' => $days,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        );
    }
    
    return $pattern;
}
?>
