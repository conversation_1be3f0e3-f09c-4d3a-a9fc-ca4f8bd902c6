<?php
session_start();

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/../includes/config.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// Get current user's employee ID
$stmt = $mysqli->prepare("SELECT employee_id FROM users WHERE id = ?");
$stmt->bind_param('i', $_SESSION['user_id']);
$stmt->execute();
$stmt->bind_result($employee_id);
$stmt->fetch();
$stmt->close();

if (!$employee_id) {
    echo json_encode(['error' => 'Employee not found']);
    exit;
}

// Get today's shift assignment with template details
$today = date('Y-m-d');
$stmt = $mysqli->prepare("
    SELECT es.*, st.name, st.start_time, st.end_time, st.description,
           st.early_checkin_allowed_hours, st.late_checkin_grace_hours, 
           st.late_checkin_policy, st.early_checkout_penalty_hours,
           st.late_checkout_overtime_hours, st.overtime_tracking_enabled,
           st.weekend_policy_enabled, st.holiday_policy_enabled
    FROM employee_shifts es
    JOIN shift_templates st ON es.shift_template_id = st.id
    WHERE es.employee_id = ? AND ? BETWEEN es.shift_start_date AND es.shift_end_date
    LIMIT 1
");
$stmt->bind_param('is', $employee_id, $today);
$stmt->execute();
$result = $stmt->get_result();
$shift_assignment = $result->fetch_assoc();
$stmt->close();

// Check if today is weekend or holiday
$is_weekend = date('w') == 0 || date('w') == 6; // Sunday = 0, Saturday = 6
$is_holiday = false;

if ($shift_assignment) {
    $holiday_stmt = $mysqli->prepare("
        SELECT COUNT(*) as count FROM holidays 
        WHERE date = ? OR (is_recurring = 1 AND DATE_FORMAT(date, '%m-%d') = DATE_FORMAT(?, '%m-%d'))
    ");
    $holiday_stmt->bind_param('ss', $today, $today);
    $holiday_stmt->execute();
    $holiday_stmt->bind_result($holiday_count);
    $holiday_stmt->fetch();
    $holiday_stmt->close();
    
    $is_holiday = $holiday_count > 0;
}

// Get applicable policy (weekend/holiday override or regular)
$applicable_policy = null;
if ($shift_assignment) {
    if ($is_holiday && $shift_assignment['holiday_policy_enabled']) {
        $policy_stmt = $mysqli->prepare("
            SELECT * FROM attendance_policies 
            WHERE shift_template_id = ? AND policy_type = 'holiday'
        ");
        $policy_stmt->bind_param('i', $shift_assignment['shift_template_id']);
        $policy_stmt->execute();
        $policy_result = $policy_stmt->get_result();
        $applicable_policy = $policy_result->fetch_assoc();
        $policy_stmt->close();
    } elseif ($is_weekend && $shift_assignment['weekend_policy_enabled']) {
        $policy_stmt = $mysqli->prepare("
            SELECT * FROM attendance_policies 
            WHERE shift_template_id = ? AND policy_type = 'weekend'
        ");
        $policy_stmt->bind_param('i', $shift_assignment['shift_template_id']);
        $policy_stmt->execute();
        $policy_result = $policy_stmt->get_result();
        $applicable_policy = $policy_result->fetch_assoc();
        $policy_stmt->close();
    }
    
    // If no special policy found, use the shift template policy
    if (!$applicable_policy) {
        $applicable_policy = $shift_assignment;
    }
}

// Check if employee has already checked in today
$checkin_status = null;
if ($employee_id) {
    $checkin_stmt = $mysqli->prepare("
        SELECT type, timestamp, attendance_status 
        FROM attendance 
        WHERE employee_id = ? AND DATE(timestamp) = CURDATE()
        ORDER BY timestamp DESC
        LIMIT 1
    ");
    $checkin_stmt->bind_param('i', $employee_id);
    $checkin_stmt->execute();
    $checkin_result = $checkin_stmt->get_result();
    $checkin_status = $checkin_result->fetch_assoc();
    $checkin_stmt->close();
}

// Prepare response
$response = [
    'employee_id' => $employee_id,
    'today' => $today,
    'is_weekend' => $is_weekend,
    'is_holiday' => $is_holiday,
    'shift' => $applicable_policy,
    'checkin_status' => $checkin_status,
    'current_time' => date('H:i:s'),
    'current_timestamp' => date('Y-m-d H:i:s')
];

echo json_encode($response);
?>
