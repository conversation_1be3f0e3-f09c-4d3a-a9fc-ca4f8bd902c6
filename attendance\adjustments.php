<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit;
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

// Check if user has permission to adjust attendance
if (!hasPermission($mysqli, $_SESSION['user_id'], 'attendance.adjust')) {
    header("Location: ../dashboard.php?error=" . urlencode("You don't have permission to adjust attendance records."));
    exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

$msg = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        if ($action === 'adjust_attendance') {
            $attendance_id = intval($_POST['attendance_id'] ?? 0);
            $adjustment_type = $_POST['adjustment_type'] ?? '';
            $reason = trim($_POST['reason'] ?? '');

            if ($attendance_id && $adjustment_type && $reason) {
                // Get original attendance record
                $stmt = $mysqli->prepare("SELECT * FROM attendance WHERE id = ?");
                $stmt->bind_param('i', $attendance_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $original_record = $result->fetch_assoc();
                $stmt->close();

                if ($original_record) {
                    $mysqli->begin_transaction();

                    try {
                        // Insert adjustment record
                        $adj_stmt = $mysqli->prepare("
                            INSERT INTO attendance_adjustments (
                                attendance_id, adjusted_by_user_id, adjustment_type, reason,
                                original_timestamp, original_status, original_overtime_hours,
                                original_break_hours, adjusted_timestamp, adjusted_status,
                                adjusted_overtime_hours, adjusted_break_hours
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");

                        // Prepare adjustment values based on type
                        $adjusted_timestamp = $_POST['adjusted_timestamp'] ?? $original_record['timestamp'];
                        $adjusted_status = $_POST['adjusted_status'] ?? $original_record['attendance_status'];
                        $adjusted_overtime = floatval($_POST['adjusted_overtime_hours'] ?? $original_record['overtime_hours']);
                        $adjusted_break = floatval($_POST['adjusted_break_hours'] ?? $original_record['break_hours']);

                        $adj_stmt->bind_param('iissssddssdd',
                            $attendance_id, $_SESSION['user_id'], $adjustment_type, $reason,
                            $original_record['timestamp'], $original_record['attendance_status'],
                            $original_record['overtime_hours'], $original_record['break_hours'],
                            $adjusted_timestamp, $adjusted_status, $adjusted_overtime, $adjusted_break
                        );

                        $adj_stmt->execute();
                        $adj_stmt->close();

                        // Update attendance record
                        $update_stmt = $mysqli->prepare("
                            UPDATE attendance SET
                                timestamp = ?, attendance_status = ?, overtime_hours = ?,
                                break_hours = ?, is_adjusted = 1
                            WHERE id = ?
                        ");
                        $update_stmt->bind_param('ssddi',
                            $adjusted_timestamp, $adjusted_status, $adjusted_overtime, $adjusted_break, $attendance_id
                        );
                        $update_stmt->execute();
                        $update_stmt->close();

                        $mysqli->commit();
                        $msg = "Attendance record adjusted successfully.";

                    } catch (Exception $e) {
                        $mysqli->rollback();
                        $error = "Failed to adjust attendance record: " . $e->getMessage();
                    }
                } else {
                    $error = "Attendance record not found.";
                }
            } else {
                $error = "Please fill in all required fields.";
            }
        }
    }
}

// Get filter parameters
$employee_filter = intval($_GET['employee_id'] ?? 0);
$date_filter = $_GET['date'] ?? date('Y-m-d');
$status_filter = $_GET['status'] ?? '';

// Build query with filters
$where_conditions = ["DATE(a.timestamp) = ?"];
$params = [$date_filter];
$param_types = 's';

if ($employee_filter) {
    $where_conditions[] = "a.employee_id = ?";
    $params[] = $employee_filter;
    $param_types .= 'i';
}

if ($status_filter) {
    $where_conditions[] = "a.attendance_status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

$where_clause = implode(' AND ', $where_conditions);

// Fetch attendance records for adjustment
$stmt = $mysqli->prepare("
    SELECT a.*, e.name as employee_name, st.name as shift_name
    FROM attendance a
    JOIN employees e ON a.employee_id = e.id
    LEFT JOIN shift_templates st ON a.shift_template_id = st.id
    WHERE $where_clause
    ORDER BY a.timestamp DESC
");

$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$attendance_records = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Fetch employees for filter
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);

// Fetch recent adjustments
$adjustments_result = $mysqli->query("
    SELECT aa.*, e.name as employee_name, u.name as adjusted_by_name
    FROM attendance_adjustments aa
    JOIN attendance a ON aa.attendance_id = a.id
    JOIN employees e ON a.employee_id = e.id
    JOIN users u ON aa.adjusted_by_user_id = u.id
    ORDER BY aa.created_at DESC
    LIMIT 10
");
$recent_adjustments = $adjustments_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Attendance Adjustments - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Attendance Adjustments</h1>
          <p class="page-subtitle">Review and manually adjust attendance records</p>
        </div>
        <div class="d-flex gap-3">
          <a href="../dashboard.php" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-funnel me-2"></i>
        Filter Attendance Records
      </h2>
      <form method="GET" class="row g-3">
        <div class="col-md-4">
          <label for="employee_id" class="form-label">Employee</label>
          <select class="form-select" id="employee_id" name="employee_id">
            <option value="">All Employees</option>
            <?php foreach ($employees as $employee): ?>
              <option value="<?= $employee['id'] ?>" <?= $employee_filter == $employee['id'] ? 'selected' : '' ?>>
                <?= htmlspecialchars($employee['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>

        <div class="col-md-4">
          <label for="date" class="form-label">Date</label>
          <input type="date" class="form-control" id="date" name="date" value="<?= htmlspecialchars($date_filter) ?>" />
        </div>

        <div class="col-md-4">
          <label for="status" class="form-label">Status</label>
          <select class="form-select" id="status" name="status">
            <option value="">All Statuses</option>
            <option value="on_time" <?= $status_filter === 'on_time' ? 'selected' : '' ?>>On Time</option>
            <option value="late" <?= $status_filter === 'late' ? 'selected' : '' ?>>Late</option>
            <option value="very_late" <?= $status_filter === 'very_late' ? 'selected' : '' ?>>Very Late</option>
            <option value="early" <?= $status_filter === 'early' ? 'selected' : '' ?>>Early</option>
            <option value="absent" <?= $status_filter === 'absent' ? 'selected' : '' ?>>Absent</option>
          </select>
        </div>

        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-search me-1"></i>
            Filter Records
          </button>
        </div>
      </form>
    </div>

    <!-- Attendance Records -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-clock-history me-2"></i>
        Attendance Records for <?= date('F j, Y', strtotime($date_filter)) ?>
      </h2>

      <?php if (empty($attendance_records)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Records Found</h4>
          <p class="text-muted">No attendance records found for the selected criteria.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Employee</th>
                <th>Type</th>
                <th>Time</th>
                <th>Status</th>
                <th>Overtime</th>
                <th>Late Hours</th>
                <th>Adjusted</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($attendance_records as $record): ?>
              <tr>
                <td>
                  <strong><?= htmlspecialchars($record['employee_name']) ?></strong>
                  <?php if ($record['shift_name']): ?>
                    <small class="text-muted d-block"><?= htmlspecialchars($record['shift_name']) ?></small>
                  <?php endif; ?>
                </td>
                <td>
                  <span class="badge bg-<?= $record['type'] === 'in' ? 'success' : 'danger' ?>">
                    <?= ucfirst($record['type']) ?>
                  </span>
                </td>
                <td><?= date('g:i A', strtotime($record['timestamp'])) ?></td>
                <td>
                  <span class="badge bg-<?=
                    $record['attendance_status'] === 'on_time' ? 'success' :
                    ($record['attendance_status'] === 'late' ? 'warning' : 'danger')
                  ?>">
                    <?= ucwords(str_replace('_', ' ', $record['attendance_status'])) ?>
                  </span>
                </td>
                <td><?= number_format($record['overtime_hours'], 2) ?>h</td>
                <td><?= number_format($record['late_hours'], 2) ?>h</td>
                <td>
                  <?php if ($record['is_adjusted']): ?>
                    <span class="badge bg-info">Adjusted</span>
                  <?php else: ?>
                    <span class="text-muted">No</span>
                  <?php endif; ?>
                </td>
                <td>
                  <button type="button" class="btn btn-outline-primary btn-sm"
                          onclick="openAdjustmentModal(<?= $record['id'] ?>, '<?= htmlspecialchars($record['employee_name']) ?>',
                                                     '<?= $record['timestamp'] ?>', '<?= $record['attendance_status'] ?>',
                                                     <?= $record['overtime_hours'] ?>, <?= $record['break_hours'] ?>)">
                    <i class="bi bi-pencil"></i>
                    Adjust
                  </button>
                </td>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <!-- Adjustment Modal -->
  <div class="modal fade" id="adjustmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Adjust Attendance Record</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form method="POST" id="adjustmentForm">
          <div class="modal-body">
            <input type="hidden" name="action" value="adjust_attendance" />
            <input type="hidden" name="attendance_id" id="adjust_attendance_id" />

            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <strong>Warning:</strong> Adjusting attendance records will create an audit trail.
              Please provide a clear reason for this adjustment.
            </div>

            <div class="row g-3">
              <div class="col-12">
                <h6>Employee: <span id="adjust_employee_name" class="text-primary"></span></h6>
              </div>

              <div class="col-md-6">
                <label for="adjustment_type" class="form-label">Adjustment Type</label>
                <select class="form-select" id="adjustment_type" name="adjustment_type" required>
                  <option value="">Select Type</option>
                  <option value="time_correction">Time Correction</option>
                  <option value="status_change">Status Change</option>
                  <option value="overtime_adjustment">Overtime Adjustment</option>
                  <option value="break_adjustment">Break Time Adjustment</option>
                </select>
              </div>

              <div class="col-md-6">
                <label for="adjusted_timestamp" class="form-label">Adjusted Time</label>
                <input type="datetime-local" class="form-control" id="adjusted_timestamp" name="adjusted_timestamp" />
              </div>

              <div class="col-md-6">
                <label for="adjusted_status" class="form-label">Adjusted Status</label>
                <select class="form-select" id="adjusted_status" name="adjusted_status">
                  <option value="on_time">On Time</option>
                  <option value="late">Late</option>
                  <option value="very_late">Very Late</option>
                  <option value="early">Early</option>
                  <option value="absent">Absent</option>
                </select>
              </div>

              <div class="col-md-3">
                <label for="adjusted_overtime_hours" class="form-label">Overtime Hours</label>
                <input type="number" class="form-control" id="adjusted_overtime_hours"
                       name="adjusted_overtime_hours" step="0.01" min="0" />
              </div>

              <div class="col-md-3">
                <label for="adjusted_break_hours" class="form-label">Break Hours</label>
                <input type="number" class="form-control" id="adjusted_break_hours"
                       name="adjusted_break_hours" step="0.01" min="0" />
              </div>

              <div class="col-12">
                <label for="reason" class="form-label">Reason for Adjustment</label>
                <textarea class="form-control" id="reason" name="reason" rows="3" required
                          placeholder="Provide a detailed reason for this adjustment..."></textarea>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-warning">
              <i class="bi bi-pencil me-1"></i>
              Apply Adjustment
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function openAdjustmentModal(attendanceId, employeeName, timestamp, status, overtimeHours, breakHours) {
      document.getElementById('adjust_attendance_id').value = attendanceId;
      document.getElementById('adjust_employee_name').textContent = employeeName;

      // Convert timestamp to datetime-local format
      const date = new Date(timestamp);
      const localDateTime = date.getFullYear() + '-' +
                           String(date.getMonth() + 1).padStart(2, '0') + '-' +
                           String(date.getDate()).padStart(2, '0') + 'T' +
                           String(date.getHours()).padStart(2, '0') + ':' +
                           String(date.getMinutes()).padStart(2, '0');

      document.getElementById('adjusted_timestamp').value = localDateTime;
      document.getElementById('adjusted_status').value = status;
      document.getElementById('adjusted_overtime_hours').value = overtimeHours;
      document.getElementById('adjusted_break_hours').value = breakHours;

      new bootstrap.Modal(document.getElementById('adjustmentModal')).show();
    }
  </script>
</body>
</html>
